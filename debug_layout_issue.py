#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局问题调试脚本
帮助诊断为什么UI修改没有生效的问题
"""

import sys
import os
import hashlib
from pathlib import Path
from datetime import datetime

def calculate_file_hash(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        return f"Error: {e}"

def check_file_modifications():
    """检查文件修改情况"""
    print("🔍 检查文件修改情况...")
    
    qml_file = Path("src/views/qml/pages/EmailGenerationPage.qml")
    backup_file = Path("src/views/qml/pages/EmailGenerationPage.qml.backup")
    
    if not qml_file.exists():
        print(f"❌ 主文件不存在: {qml_file}")
        return False
    
    if not backup_file.exists():
        print(f"⚠️ 备份文件不存在: {backup_file}")
    else:
        # 比较文件哈希
        main_hash = calculate_file_hash(qml_file)
        backup_hash = calculate_file_hash(backup_file)
        
        if main_hash == backup_hash:
            print("❌ 主文件和备份文件相同，修改可能没有保存！")
            return False
        else:
            print("✅ 主文件和备份文件不同，修改已保存")
    
    # 检查文件修改时间
    stat = qml_file.stat()
    mod_time = datetime.fromtimestamp(stat.st_mtime)
    print(f"📅 文件最后修改时间: {mod_time}")
    
    return True

def check_problematic_code():
    """检查是否还存在问题代码"""
    print("\n🔍 检查问题代码...")
    
    qml_file = Path("src/views/qml/pages/EmailGenerationPage.qml")
    
    try:
        with open(qml_file, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        # 检查是否还有空的fillHeight Item
        problematic_patterns = [
            "Item { Layout.fillHeight: true }",
            "Item{Layout.fillHeight:true}",
            "Item { Layout.fillHeight:true }",
            "Item {Layout.fillHeight: true}"
        ]
        
        found_problems = []
        for i, line in enumerate(lines, 1):
            for pattern in problematic_patterns:
                if pattern in line:
                    found_problems.append(f"第{i}行: {line.strip()}")
        
        if found_problems:
            print("❌ 仍然存在问题代码:")
            for problem in found_problems:
                print(f"   {problem}")
            return False
        else:
            print("✅ 没有发现空的fillHeight Item")
        
        # 检查其他可能的问题
        large_heights = []
        for i, line in enumerate(lines, 1):
            # 查找大的固定高度设置
            if "height:" in line and any(str(h) in line for h in range(100, 1000)):
                large_heights.append(f"第{i}行: {line.strip()}")
        
        if large_heights:
            print("\n⚠️ 发现较大的固定高度设置:")
            for height in large_heights:
                print(f"   {height}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查文件时出错: {e}")
        return False

def provide_debugging_steps():
    """提供调试步骤"""
    print("\n🛠️ 调试步骤建议:")
    print("1. 【重启应用程序】")
    print("   - 完全关闭应用程序")
    print("   - 重新运行 python run.py 或 python src/main.py")
    print("   - 确保QML文件被重新加载")
    
    print("\n2. 【清除可能的缓存】")
    print("   - 删除 __pycache__ 目录")
    print("   - 删除 .qmlc 文件（如果存在）")
    print("   - 重新启动应用程序")
    
    print("\n3. 【验证修改生效】")
    print("   - 在QML文件中添加临时的调试信息")
    print("   - 例如：在第1284行添加 Text { text: '调试：修改已生效' }")
    print("   - 重启应用查看是否显示")
    
    print("\n4. 【检查其他可能的问题】")
    print("   - 检查是否有其他QML文件影响布局")
    print("   - 检查主窗口或父容器的设置")
    print("   - 查看控制台是否有QML错误信息")

def main():
    print("🔧 布局问题调试工具")
    print("=" * 50)
    
    # 检查文件修改
    file_ok = check_file_modifications()
    
    # 检查问题代码
    code_ok = check_problematic_code()
    
    # 提供调试建议
    provide_debugging_steps()
    
    print("\n" + "=" * 50)
    if file_ok and code_ok:
        print("✅ 文件修改正常，问题可能是缓存或重载问题")
        print("💡 建议：完全重启应用程序")
    else:
        print("❌ 发现文件或代码问题，请检查上述输出")
    
    return file_ok and code_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
