#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清除缓存并重启应用程序的脚本
解决QML修改不生效的问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clear_python_cache():
    """清除Python缓存"""
    print("🧹 清除Python缓存...")
    
    # 清除__pycache__目录
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                cache_path = Path(root) / dir_name
                try:
                    shutil.rmtree(cache_path)
                    print(f"✅ 删除缓存目录: {cache_path}")
                except Exception as e:
                    print(f"⚠️ 无法删除 {cache_path}: {e}")
    
    # 清除.pyc文件
    pyc_count = 0
    for root, dirs, files in os.walk('.'):
        for file_name in files:
            if file_name.endswith('.pyc'):
                pyc_path = Path(root) / file_name
                try:
                    pyc_path.unlink()
                    pyc_count += 1
                except Exception as e:
                    print(f"⚠️ 无法删除 {pyc_path}: {e}")
    
    if pyc_count > 0:
        print(f"✅ 删除了 {pyc_count} 个 .pyc 文件")

def clear_qml_cache():
    """清除QML缓存"""
    print("🧹 清除QML缓存...")
    
    # 查找并删除.qmlc文件
    qmlc_count = 0
    for root, dirs, files in os.walk('.'):
        for file_name in files:
            if file_name.endswith('.qmlc'):
                qmlc_path = Path(root) / file_name
                try:
                    qmlc_path.unlink()
                    qmlc_count += 1
                    print(f"✅ 删除QML缓存: {qmlc_path}")
                except Exception as e:
                    print(f"⚠️ 无法删除 {qmlc_path}: {e}")
    
    if qmlc_count == 0:
        print("ℹ️ 没有找到QML缓存文件")

def kill_existing_processes():
    """尝试终止现有的应用程序进程"""
    print("🔄 检查并终止现有进程...")
    
    try:
        # 在Windows上查找Python进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True)
        if 'python.exe' in result.stdout:
            print("⚠️ 发现运行中的Python进程，请手动关闭应用程序")
        else:
            print("✅ 没有发现运行中的Python进程")
    except Exception as e:
        print(f"ℹ️ 无法检查进程状态: {e}")

def restart_application():
    """重启应用程序"""
    print("🚀 准备重启应用程序...")
    
    # 检查启动脚本
    start_scripts = ['run.py', 'src/main.py']
    
    for script in start_scripts:
        if Path(script).exists():
            print(f"📋 建议运行命令: python {script}")
            
            # 询问是否自动启动
            try:
                choice = input("是否自动启动应用程序？(y/n): ").lower().strip()
                if choice in ['y', 'yes', '是']:
                    print(f"🚀 启动应用程序: python {script}")
                    subprocess.Popen([sys.executable, script])
                    return True
                else:
                    print("💡 请手动运行上述命令启动应用程序")
                    return False
            except KeyboardInterrupt:
                print("\n💡 请手动启动应用程序")
                return False
    
    print("❌ 没有找到启动脚本")
    return False

def main():
    print("🔧 清除缓存并重启应用程序")
    print("=" * 50)
    
    # 清除各种缓存
    clear_python_cache()
    clear_qml_cache()
    
    # 检查进程
    kill_existing_processes()
    
    print("\n" + "=" * 50)
    print("✅ 缓存清除完成")
    
    # 重启应用程序
    restart_application()
    
    print("\n💡 重启后请检查：")
    print("   1. 界面底部是否显示绿色的调试信息")
    print("   2. 如果显示调试信息，说明修改已生效")
    print("   3. 如果空隙问题仍然存在，可能需要进一步分析")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 操作已取消")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
