# 域名邮箱管理器 - 开发计划文档

## 📋 项目概述

开发一个简单的域名邮箱管理器桌面应用程序，主要功能是生成、存储和管理基于自定义域名的邮箱地址。这是一个轻量级的邮箱管理工具，专注于邮箱地址的生成和组织管理。

**双工作区并行开发说明：**
为了提高开发效率并支持多功能并行开发，本项目将采用双工作区（Git Worktree）并行开发模式。这意味着开发任务将被划分为两个主要并行轨道，每个轨道在一个独立的工作区中进行，以最大程度地减少代码冲突并优化协作流程。

## 🛠️ 技术栈选择

### 推荐技术栈：PyQt6 + SQLite + PyInstaller

**前端框架：PyQt6 + QML (重点)**
- **QML现代化声明式UI：** 默认推荐，用于构建流畅、响应迅速的用户界面。
- **动画设计：** 利用QML强大的动画系统，实现平滑的过渡、加载动画和交互反馈，增强用户体验。
- **UI/UX设计：** 遵循Material Design原则，确保界面美观、直观且易于使用。
- **视觉体验方案：** 统一的色彩、字体、图标和组件库，提供一致且专业的视觉感受。
- **与现有Python代码完美集成：** 通过PyQt的QObject机制实现QML与Python的无缝通信。
- **跨平台支持：** Windows/Mac/Linux。
- **GPU加速渲染和响应式布局：** 确保高性能和适应不同屏幕尺寸。

**数据库：SQLite**
- 轻量级，无需额外安装。
- 支持复杂查询和事务。
- Python原生支持。
- 适合桌面应用数据量。

**打包工具：PyInstaller**
- 成熟稳定，支持PyQt。
- 单文件exe分发。
- 支持图标和版本信息。
- 可隐藏控制台窗口。

## 📁 项目结构设计

```
email-domain-manager/
├── src/                          # 源代码目录
│   ├── main.py                   # 应用程序入口 (主要由前端工作区维护)
│   ├── models/                   # 数据模型层 (主要由后端工作区维护)
│   │   ├── __init__.py
│   │   ├── email_model.py        # 邮箱数据模型
│   │   ├── config_model.py       # 配置数据模型
│   │   └── tag_model.py          # 标签数据模型
│   ├── services/                 # 业务逻辑层 (主要由后端工作区维护)
│   │   ├── __init__.py
│   │   ├── email_service.py      # 邮箱生成和管理服务
│   │   ├── config_service.py     # 配置管理服务
│   │   └── database_service.py   # 数据库服务
│   ├── controllers/              # 控制器层 (前后端交互接口)
│   │   ├── __init__.py
│   │   ├── email_controller.py   # 邮箱控制器
│   │   └── config_controller.py  # 配置控制器
│   ├── views/                    # 视图层 (主要由前端工作区维护)
│   │   ├── __init__.py
│   │   ├── main_window.py        # 主窗口 (Python部分)
│   │   ├── qml/                  # QML文件目录
│   │   │   ├── main.qml          # 主QML文件
│   │   │   ├── components/       # 可复用QML组件
│   │   │   └── pages/            # 各页面QML文件
│   │   ├── email_generation_page.py  # 邮箱申请页面 (Python部分，与QML交互)
│   │   ├── email_management_page.py  # 邮箱管理页面 (Python部分，与QML交互)
│   │   └── configuration_page.py     # 配置管理页面 (Python部分，与QML交互)
│   ├── utils/                    # 工具类 (前后端共享，但主要由后端工作区维护)
│   │   ├── __init__.py
│   │   ├── crypto_utils.py       # 加密工具
│   │   ├── file_utils.py         # 文件操作工具
│   │   └── logger.py             # 日志工具
│   └── resources/                # 资源文件 (前后端共享)
│       ├── icons/                # 图标文件
│       ├── styles/               # 样式文件 (QML样式)
│       └── database/             # 数据库初始化脚本
├── tests/                        # 测试代码
├── docs/                         # 文档目录
├── requirements.txt              # 依赖包列表
├── build.py                      # 构建脚本
└── README.md                     # 项目说明
```

## 🚀 开发阶段规划 (并行轨道)

为了最大化并行开发效率并最小化冲突，我们将开发流程划分为两个主要轨道：

### 轨道 A: 核心后端与数据管理 (Worktree A)

**主要职责：** 负责所有后端业务逻辑、数据模型、数据库交互、配置管理和核心算法的实现。此轨道将专注于提供稳定的API接口供前端调用。

**Phase 1A: 基础架构与核心模块 (Week 1-2)**

**任务清单：**
- [x] 创建项目目录结构（后端相关部分）
- [x] 设置开发环境和依赖管理（后端依赖）
- [x] 实现简单的邮箱生成器（EmailGenerator）
- [x] 设计数据库表结构（Email, Config, Tag）
- [x] 初始化数据库服务 `src/services/database_service.py`

**关键文件：**
- `src/services/email_service.py` - 邮箱生成和管理功能
- `src/services/database_service.py` - 数据库初始化与CRUD操作
- `src/models/*.py` - 数据模型定义

**验收标准：**
- 数据库可以正常创建和连接。
- 邮箱生成逻辑可以独立运行并返回预期结果。
- 数据模型定义清晰，与数据库结构一致。

**Phase 2A: 核心业务逻辑与数据持久化 (Week 3-4)**

**任务清单：**
- [ ] 实现EmailService的完整功能（生成、存储、删除、查询）
- [ ] 实现ConfigService配置管理（读取、写入）
- [ ] 实现DatabaseService的数据持久化（增删改查）
- [ ] 实现TagService（标签的CRUD操作）

**关键功能：**
- 邮箱生成算法（基于域名和自定义规则）。
- 配置文件管理和加密。
- 数据库CRUD操作的稳定性和性能。

**验收标准：**
- 可以成功生成域名邮箱并保存到数据库。
- 配置可以正确保存和加载，并能通过API进行管理。
- 邮箱和标签记录可以正确存储、查询和更新。

**Phase 3A: 高级后端功能与安全性 (Week 5-7)**

**任务清单：**
- [ ] 实现标签系统（创建、编辑、删除、关联邮箱）的后端逻辑。
- [ ] 实现搜索和筛选功能的后端查询接口。
- [ ] 实现数据导出功能（CSV/JSON）的后端数据准备。
- [ ] 实现批量操作的后端接口。
- [ ] 实现配置文件加密和敏感数据保护的后端机制。

**安全性设计：**
- AES加密敏感配置。
- 内存数据及时清理。
- 日志脱敏处理。

**验收标准：**
- 标签系统后端API完整可用。
- 搜索筛选后端接口响应迅速且准确。
- 导出功能后端数据准备正确无误。
- 敏感信息得到保护，加密解密流程正常。

### 轨道 B: 前端 UI/UX 与集成 (Worktree B)

**主要职责：** 负责所有用户界面（UI）的设计、开发、动画实现、视觉优化以及与后端API的集成。此轨道将专注于提供卓越的用户体验。

**Phase 1B: 基础UI框架与QML环境搭建 (Week 1-2)**

**任务清单：**
- [ ] 创建基础的PyQt6应用框架和QML加载机制。
- [ ] 设置QML开发环境和工具（如Qt Design Studio）。
- [ ] 设计并实现主窗口 (`src/views/main_window.py` 和 `src/views/qml/main.qml`)。
- [ ] 建立QML与Python后端控制器 (`src/controllers/`) 的基本通信机制。

**关键文件：**
- `src/main.py` - 应用程序入口，加载QML。
- `src/views/main_window.py` - Python主窗口类。
- `src/views/qml/main.qml` - 主QML文件。
- `src/controllers/*.py` - 定义前端可调用的Python接口。

**验收标准：**
- 应用程序可以启动并显示带有基本布局的主窗口。
- QML界面可以正常加载和渲染。
- QML与Python之间可以进行简单的双向通信。

**Phase 2B: 核心页面UI与交互 (Week 3-4)**

**任务清单：**
- [ ] 开发邮箱申请页面UI (`src/views/qml/pages/email_generation_page.qml`)。
- [ ] 开发邮箱管理页面UI (`src/views/qml/pages/email_management_page.qml`)。
- [ ] 开发配置管理页面UI (`src/views/qml/pages/configuration_page.qml`)。
- [ ] 实现页面间导航和数据传递（前端层面）。
- [ ] 添加基础的加载动画和状态提示（如进度条）。

**页面详细设计 (QML实现)：**

**2.1 邮箱申请页面**
- 左侧面板：配置概览（如当前使用的域名）。
- 中央区域：生成按钮和交互反馈（动画效果）。
- 底部状态栏：操作状态提示（动画效果）。

**2.2 邮箱管理页面**
- 顶部工具栏：搜索框、筛选器、刷新按钮。
- 主表格：邮箱列表（邮箱地址、创建时间、标签，通过后端API获取）。
- 右侧面板：标签管理和详细信息（通过后端API获取）。
- 底部操作栏：导出、删除、批量操作按钮。

**2.3 配置管理页面**
- 分组配置表单：域名设置、数据导入/导出设置。
- 导入/导出功能：调用后端API。
- 帮助文档：配置说明和示例。

**验收标准：**
- 所有页面UI完整且美观，符合Material Design风格。
- 页面间切换流畅，带有过渡动画。
- 基础交互功能正常，能调用后端API并显示结果。

**Phase 3B: 高级UI/UX与视觉增强 (Week 5-7)**

**任务清单：**
- [ ] 实现标签系统的UI交互（创建、编辑、删除、关联）。
- [ ] 实现搜索和筛选功能的UI逻辑。
- [ ] 实现数据导出功能的UI流程和进度显示。
- [ ] 实现批量操作的UI交互。
- [ ] 优化整体视觉体验：统一字体、颜色、图标。
- [ ] 添加更复杂的动画效果（如列表项进入/退出动画、按钮点击反馈）。
- [ ] 确保UI的响应式布局，适应不同屏幕尺寸。

**QML优化设计：**
- **性能优化：**
    - 避免在QML中进行复杂的计算，将逻辑尽可能下沉到Python后端。
    - 使用 `Loader` 组件进行按需加载，避免一次性加载所有UI元素。
    - 优化 `Repeater` 和 `ListView` 的Delegate，减少不必要的元素创建和属性绑定。
    - 谨慎使用 `Connections`，避免循环引用和内存泄漏。
- **代码结构：**
    - 将可复用的UI组件封装到 `src/views/qml/components/` 目录。
    - 将每个主要页面拆分为独立的QML文件，放置在 `src/views/qml/pages/`。

**动画设计：**
- **类型：**
    - **Transitions (过渡):** 用于页面切换、组件显示/隐藏时的平滑过渡。
    - **PropertyAnimation (属性动画):** 改变组件的属性（如位置、大小、透明度、颜色）。
    - **SequentialAnimation (序列动画):** 动画按顺序播放。
    - **ParallelAnimation (并行动画):** 动画同时播放。
- **原则：**
    - **缓动曲线 (Easing Curves):** 使用合适的缓动曲线使动画更自然、流畅。
    - **持续时间 (Duration):** 动画持续时间适中，不宜过长或过短。
    - **延迟 (Delay):** 适当的延迟可以增强动画的节奏感。
- **工具：**
    - 推荐使用 **Qt Design Studio** 进行QML UI和动画的可视化设计。

**UI设计方案：**
- **Material Design原则：**
    - **层次感：** 利用阴影和Z轴深度来区分元素。
    - **反馈：** 按钮点击、列表选择等操作应有即时视觉反馈。
    - **一致性：** 保持组件、布局和交互模式的一致性。
    - **可访问性：** 考虑颜色对比度、字体大小和键盘导航。
- **响应式布局：**
    - 使用 `Anchors` 和 `Layouts` (如 `RowLayout`, `ColumnLayout`, `GridLayout`) 来构建灵活的UI。
    - 考虑不同屏幕分辨率和DPI设置。

**增强视觉体验方案：**
- **图标设计：** 采用一套统一、现代且易于识别的图标库（如Material Icons）。
- **字体排版：** 选择清晰易读的字体，并定义不同层级的字体大小和粗细，建立视觉层次。
- **色彩搭配：** 建立主色、辅色和强调色，确保色彩和谐且符合品牌调性。考虑深色模式支持。
- **微交互：** 细小的动画和反馈，如按钮悬停效果、输入框焦点动画，提升用户愉悦感。

**验收标准：**
- 所有高级UI功能（标签、搜索、导出、批量操作）交互流畅。
- 动画效果自然、平滑，提升用户体验。
- 界面美观，符合Material Design风格，响应式布局良好。

## 🤝 冲突避免策略

为了确保双工作区并行开发的顺利进行，我们将严格遵循以下策略：

1.  **明确职责边界：**
    *   **轨道 A (后端):** 专注于 `src/models/`, `src/services/`, `src/utils/` (除UI相关日志外), `data/`, `resources/database/`。
    *   **轨道 B (前端):** 专注于 `src/views/`, `src/main.py`, `resources/icons/`, `resources/styles/`。
    *   `src/controllers/` 目录下的文件将作为前后端交互的桥梁，由双方共同维护，但需提前定义好接口规范。
    *   `src/utils/logger.py` 和 `src/utils/config_manager.py` 等共享工具类，主要由后端工作区维护，前端工作区仅调用其提供的接口。

2.  **API契约优先：**
    *   在开发初期，前后端团队需共同定义清晰的API接口（方法签名、输入参数、返回数据结构）。
    *   一旦API契约确定，双方应严格遵守，任何一方需要修改API时，必须提前与另一方沟通并达成一致。

3.  **最小化共享文件修改：**
    *   尽量避免同时修改同一个文件。如果必须修改，应提前沟通，并确保修改范围最小化。
    *   对于 `main.py`，前端工作区负责其主要结构和QML加载，后端工作区仅在必要时（如初始化服务）进行少量修改。

4.  **频繁同步与Rebase：**
    *   两个工作区应频繁从 `develop` 分支拉取最新代码并进行 `rebase` 操作，而不是 `merge`，以保持提交历史的整洁。
    *   在 `rebase` 过程中遇到冲突时，应立即解决。

5.  **代码审查：**
    *   所有提交到 `develop` 分支的PR都必须经过严格的代码审查，特别是涉及跨轨道修改的PR，以确保兼容性和避免潜在冲突。

## 📊 开发里程碑

| 里程碑 | 时间 | 主要交付物 (轨道 A) | 主要交付物 (轨道 B) | 验收标准 |
|--------|------|---------------------|---------------------|----------|
| M1     | Week 2 | 核心后端服务API就绪，数据库初始化 | 基础UI框架搭建，主窗口显示，QML与Python通信 | 应用可启动，后端服务可独立测试，基础UI可渲染 |
| M2     | Week 4 | 核心业务逻辑与数据持久化完成，标签系统后端API | 核心页面UI完成，基础交互与动画 | 邮箱生成/管理后端功能完整，所有主要页面UI完整且可交互 |
| M3     | Week 7 | 高级后端功能与安全性实现 | 高级UI/UX与视觉增强完成 | 标签、搜索、导出等高级功能后端支持，前端界面美观流畅，动画丰富 |
| M4     | Week 9 | 最终后端集成与测试 | 最终前端集成与测试 | 所有功能模块集成，系统稳定，用户体验良好 |
| M5     | Week 11| 打包配置与跨平台测试 | 打包配置与跨平台测试 | 可分发的exe程序，敏感信息保护，在目标平台正常工作 |

## 🔧 开发环境配置

**必需软件：**
- Python 3.9+
- PyQt6
- SQLite3
- PyInstaller
- **Qt Design Studio (推荐用于QML可视化设计)**

**推荐IDE：**
- PyCharm Professional
- VS Code + Python扩展

**开发工具：**
- Qt Designer (UI设计)
- DB Browser for SQLite (数据库管理)
- Git (版本控制)
- **Git Worktree (并行开发)**

## 📝 代码规范

**Python代码规范：**
- 遵循PEP 8标准
- 使用类型提示
- 完整的文档字符串
- 单元测试覆盖

**QML/JavaScript代码规范：**
- 遵循QML编码风格指南。
- 保持代码简洁、可读性高。
- 避免在QML中编写复杂业务逻辑。

**Git提交规范：**
- feat: 新增功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试相关
- ci: CI/CD 配置文件和脚本的更改
- chore: 其他不修改源代码或测试文件的杂项更改

## 🎯 质量保证

**测试策略：**
- 单元测试：核心业务逻辑（后端）和QML组件（前端）。
- 集成测试：模块间交互，前后端API调用。
- UI测试：界面功能验证，动画效果。
- 性能测试：大数据量处理，UI响应速度。

**代码审查：**
- 每个功能模块完成后进行代码审查。
- 关注安全性、性能、可维护性。
- 确保符合架构设计原则。