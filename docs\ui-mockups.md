# 域名邮箱管理器 - 简化版界面设计

## 🎨 设计概述

本文档描述了简化版域名邮箱管理器的用户界面设计，专注于邮箱生成、存储和管理的核心功能。采用现代化的Material Design风格，界面简洁直观，易于使用。

## 🖼️ 整体布局设计

### 主窗口结构

```
┌─────────────────────────────────────────────────────────────────┐
│  📧 域名邮箱管理器                                    🔧 ⚙️ ❌    │
├─────────────────────────────────────────────────────────────────┤
│  文件(F)  编辑(E)  工具(T)  帮助(H)                              │
├─────────────────────────────────────────────────────────────────┤
│  🏠 邮箱生成  │  📋 邮箱管理  │  🏷️ 标签管理  │  ⚙️ 配置        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                     [当前选中页面内容]                           │
│                                                                 │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│  状态: 就绪  │  域名: example.com  │  邮箱总数: 25  │  12:34:56   │
└─────────────────────────────────────────────────────────────────┘
```

**设计要点：**
- 窗口最小尺寸：1024x768
- 推荐尺寸：1280x800
- 支持窗口大小调整
- 四个主要功能页面
- 统一的状态栏显示关键信息

## 📄 页面详细设计

### 1. 邮箱生成页面 (EmailGenerationPage)

#### 1.1 布局结构

```
┌─────────────────────────────────────────────────────────────────┐
│  🏠 邮箱生成                                                     │
├─────────────────┬─────────────────────┬─────────────────────────┤
│   生成配置       │      操作区域        │       结果显示          │
│                │                     │                         │
│ 📍 当前域名      │   🎯 生成邮箱        │  � 生成结果            │
│ example.com     │                     │                         │
│                │  ┌─────────────────┐ │  ┌─────────────────────┐ │
│ 📧 生成模式      │  │   生成新邮箱     │ │  │ <EMAIL> │ │
│ ○ 随机名字      │  │                 │ │  │ ✅ 生成成功          │ │
│ ○ 随机字符串    │  └─────────────────┘ │  │ 📅 2025-01-22       │ │
│ ● 自定义前缀    │                     │  │ 🏷️ [测试] [开发]     │ │
│                │  ┌─────────────────┐ │  │                     │ │
│ 🔤 自定义前缀    │  │   批量生成       │ │  └─────────────────────┘ │
│ [demo_user]     │  │   (数量: 5)      │ │                         │
│                │  └─────────────────┘ │  📊 统计信息             │
│ 📊 统计信息      │                     │  ┌─────────────────────┐ │
│ 今日生成: 5     │  🏷️ 标签选择         │  │ 总邮箱数: 25        │ │
│ 总数: 25       │  ☑️ 测试用           │  │ 今日创建: 5         │ │
│                │  ☑️ 开发用           │  │ 活跃状态: 20        │ │
│                │  ☐ 生产用           │  │ 归档状态: 5         │ │
│                │                     │  └─────────────────────┘ │
└─────────────────┴─────────────────────┴─────────────────────────┘
```

#### 1.2 组件详细设计

**左侧配置面板 (220px宽度)：**
- 当前域名显示（大字体，突出显示）
- 邮箱生成模式选择（单选按钮组）
- 自定义前缀输入框（当选择自定义时启用）
- 实时统计信息（卡片样式）

**中央操作区域 (280px宽度)：**
- 生成新邮箱按钮（主要操作）
- 批量生成按钮（带数量选择）
- 标签选择器（多选复选框）
- 备注输入框

**右侧结果区域 (剩余宽度)：**
- 最新生成的邮箱显示
- 生成状态和时间
- 关联的标签显示
- 整体统计信息

#### 1.3 交互流程

```
用户选择生成模式
    ↓
输入自定义前缀（如需要）
    ↓
选择标签和备注
    ↓
点击生成按钮
    ↓
显示生成结果和统计更新
```

### 2. 邮箱管理页面 (EmailManagementPage)

#### 2.1 布局结构

```
┌─────────────────────────────────────────────────────────────────┐
│  📋 邮箱管理                                                     │
├─────────────────────────────────────────────────────────────────┤
│  🔍 [搜索框]  📅 时间筛选  🏷️ 标签筛选  📊 状态筛选  🔄 刷新    │
├─────────────────────────────────────────┬───────────────────────┤
│                邮箱列表                  │     详情面板          │
│ ┌─────────────────────────────────────┐ │                       │
│ │☑️│邮箱地址        │域名  │创建时间│状态│ │  📝 邮箱详情           │
│ ├─┼──────────────┼────┼──────┼──┤ │                       │
│ │☑️│<EMAIL>│ex.com│今天 │✅│ │  ┌─────────────────┐   │
│ │☑️│<EMAIL>│ex.com│昨天 │📊│ │  │ 📧 邮箱地址       │   │
│ │☑️│<EMAIL>│ex.com│2天前│📁│ │  │ <EMAIL>  │   │
│ │☑️│<EMAIL>│ex.com│3天前│✅│ │  │                 │   │
│ │☑️│...           │...   │... │..│ │  │ 📅 创建时间       │   │
│ └─────────────────────────────────────┘ │  │ 2025-01-22 10:30│   │
│                                         │  │                 │   │
│ 📄 第1页，共3页  ⬅️ ➡️                   │  │ � 状态: 活跃     │   │
│                                         │  │                 │   │
│ ☑️ 全选  🗑️ 删除  📤 导出  🏷️ 批量标签    │  │ 🏷️ 标签          │   │
│                                         │  │ [测试] [开发]   │   │
│                                         │  │                 │   │
│                                         │  │ 📝 备注          │   │
│                                         │  │ 测试用邮箱      │   │
│                                         │  └─────────────────┘   │
└─────────────────────────────────────────┴───────────────────────┘
```

#### 2.2 表格列设计

| 列名 | 宽度 | 说明 |
|------|------|------|
| 选择框 | 40px | 多选复选框 |
| 邮箱地址 | 250px | 主要信息，可点击复制 |
| 域名 | 120px | 域名部分 |
| 创建时间 | 120px | 相对时间显示 |
| 状态 | 80px | 活跃/非活跃/归档 |
| 标签 | 150px | 标签徽章显示 |
| 操作 | 100px | 编辑、删除按钮 |

#### 2.3 筛选功能设计

**搜索框：**
- 实时搜索（输入延迟500ms）
- 支持邮箱地址和备注模糊匹配
- 搜索历史记录

**时间筛选：**
- 今天、昨天、本周、本月
- 自定义时间范围选择器

**标签筛选：**
- 多选标签过滤
- 标签计数显示
- "未标记"选项

**状态筛选：**
- 活跃、非活跃、归档状态
- 状态图标显示

### 3. 标签管理页面 (TagManagementPage)

#### 3.1 布局结构

```
┌─────────────────────────────────────────────────────────────────┐
│  🏷️ 标签管理                                                     │
├─────────────────────────────────────────┬───────────────────────┤
│                标签列表                  │     标签编辑面板       │
│                                         │                       │
│ ┌─────────────────────────────────────┐ │  📝 标签详情           │
│ │ 🧪 测试用     │ #e74c3c │ 12 │ 编辑 │ │  ┌─────────────────┐   │
│ │ 💻 开发用     │ #3498db │ 8  │ 编辑 │ │  │ 🏷️ 标签名称      │   │
│ │ 🚀 生产用     │ #2ecc71 │ 5  │ 编辑 │ │  │ [测试用]        │   │
│ │ ⏰ 临时用     │ #f39c12 │ 3  │ 编辑 │ │  │                 │   │
│ │ 📧 邮件营销   │ #9b59b6 │ 0  │ 编辑 │ │  │ 📝 描述          │   │
│ │ ...          │ ...     │... │ ... │ │  │ [用于测试目的]  │   │
│ └─────────────────────────────────────┘ │  │                 │   │
│                                         │  │ 🎨 颜色          │   │
│ ➕ 新建标签                              │  │ ⬜ #e74c3c      │   │
│                                         │  │                 │   │
│ 📊 标签统计:                             │  │ 🎭 图标          │   │
│ • 总标签数: 5                           │  │ [🧪]            │   │
│ • 用户标签: 4                           │  │                 │   │
│ • 系统标签: 1                           │  │ 📊 使用统计      │   │
│                                         │  │ 使用次数: 12    │   │
│                                         │  │                 │   │
│                                         │  │ [💾 保存] [❌ 取消] │   │
│                                         │  └─────────────────┘   │
└─────────────────────────────────────────┴───────────────────────┘
```

#### 3.2 功能设计

**标签列表：**
- 标签名称、颜色、使用次数显示
- 可排序（按名称、使用次数、创建时间）
- 快速编辑和删除操作

**标签编辑面板：**
- 标签名称输入
- 描述文本框
- 颜色选择器
- 图标选择器
- 使用统计显示

**操作功能：**
- 新建标签
- 编辑标签
- 删除标签（检查使用情况）
- 批量操作

### 4. 配置页面 (ConfigurationPage)

#### 4.1 布局结构

```
┌─────────────────────────────────────────────────────────────────┐
│  ⚙️ 配置管理                                                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─ 🌐 域名配置 ─────────────────────────────────────────────┐   │
│  │                                                           │   │
│  │  域名地址: [example.com                    ] 🔍 验证      │   │
│  │  状态: ✅ 域名格式正确                                     │   │
│  │  说明: 用于生成邮箱地址的域名                              │   │
│  │                                                           │   │
│  └───────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─ � 安全配置 ─────────────────────────────────────────────┐   │
│  │                                                           │   │
│  │  主密码: [••••••••••••••        ] 🔄 更改密码             │   │
│  │  数据加密: ☑️ 加密敏感配置                                │   │
│  │  自动锁定: ☑️ 启用 (闲置 [30] 分钟后)                     │   │
│  │  日志级别: [信息 ▼] (调试/信息/警告/错误)                  │   │
│  │                                                           │   │
│  └───────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─ � 数据管理 ─────────────────────────────────────────────┐   │
│  │                                                           │   │
│  │  配置导出: [📤 导出配置] [📥 导入配置]                     │   │
│  │  数据备份: [💾 备份数据] 最后备份: 2025-01-22 10:30       │   │
│  │  数据清理: [🗑️ 清理数据] (清理已删除的邮箱记录)           │   │
│  │  数据库优化: [⚡ 优化数据库] (整理碎片，提升性能)          │   │
│  │                                                           │   │
│  └───────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─ � 应用设置 ─────────────────────────────────────────────┐   │
│  │                                                           │   │
│  │  启动设置: ☑️ 开机自启动                                  │   │
│  │  窗口设置: ☑️ 记住窗口大小和位置                          │   │
│  │  通知设置: ☑️ 显示操作完成通知                            │   │
│  │  主题设置: [浅色主题 ▼] (浅色/深色/跟随系统)              │   │
│  │                                                           │   │
│  └───────────────────────────────────────────────────────────┘   │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              [💾 保存配置]  [🔄 重置]  [❌ 取消]            │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### 4.2 配置分组设计

**域名配置组：**
- 域名输入框 + 格式验证
- 域名状态显示
- 使用说明

**安全配置组：**
- 主密码设置和修改
- 数据加密选项
- 自动锁定配置
- 日志级别设置

**数据管理组：**
- 配置导入导出
- 数据备份功能
- 数据清理工具
- 数据库优化

**应用设置组：**
- 启动和窗口设置
- 通知设置
- 主题选择

## 🎨 视觉设计规范

### 1. 颜色方案

**主色调：**
- 主色：#3498db (蓝色)
- 辅助色：#2ecc71 (绿色)
- 警告色：#f39c12 (橙色)
- 错误色：#e74c3c (红色)

**状态颜色：**
- 活跃：#27ae60 (绿色)
- 非活跃：#f39c12 (橙色)
- 归档：#95a5a6 (灰色)
- 错误：#e74c3c (红色)
- 信息：#3498db (蓝色)

**背景颜色：**
- 主背景：#ffffff
- 次背景：#f8f9fa
- 边框：#dee2e6
- 阴影：rgba(0,0,0,0.1)

### 2. 字体规范

**字体族：**
- 中文：Microsoft YaHei, 微软雅黑
- 英文：Segoe UI, Arial, sans-serif
- 等宽：Consolas, Monaco, monospace

**字体大小：**
- 标题：18px (bold)
- 子标题：16px (medium)
- 正文：14px (regular)
- 小字：12px (regular)
- 代码：13px (monospace)

### 3. 间距规范

**内边距：**
- 小：8px
- 中：16px
- 大：24px
- 特大：32px

**外边距：**
- 组件间：16px
- 分组间：24px
- 页面边距：20px

### 4. 图标设计

**图标库：**
- 使用Material Design Icons
- 统一16px或24px尺寸
- 支持主题色彩

**状态图标：**
- ✅ 活跃状态
- 📊 非活跃状态
- 📁 归档状态
- ❌ 错误状态
- 🔄 刷新/重试
- 🏷️ 标签
- 📧 邮箱

## 📱 响应式设计

### 1. 窗口大小适配

**最小窗口 (1024x768)：**
- 隐藏部分次要信息
- 紧凑布局模式
- 滚动条支持

**标准窗口 (1280x800)：**
- 完整功能显示
- 舒适的间距
- 最佳用户体验

**大窗口 (1920x1080+)：**
- 更多信息展示
- 宽松的布局
- 充分利用空间

### 2. 组件自适应

**表格组件：**
- 列宽自动调整
- 水平滚动支持
- 列显示/隐藏选项

**侧边栏：**
- 可折叠设计
- 最小化状态
- 鼠标悬停展开

## 🔧 交互设计

### 1. 操作反馈

**按钮状态：**
- 正常、悬停、按下、禁用
- 加载状态动画
- 操作确认提示

**表单验证：**
- 实时验证反馈
- 错误信息提示
- 成功状态确认

### 2. 快捷操作

**键盘快捷键：**
- Ctrl+N: 生成新邮箱
- Ctrl+F: 搜索邮箱
- Ctrl+S: 保存配置
- F5: 刷新列表
- Ctrl+T: 新建标签
- Delete: 删除选中项

**右键菜单：**
- 复制邮箱地址
- 编辑邮箱信息
- 添加/移除标签
- 更改状态
- 删除记录

### 3. 拖拽支持

**标签管理：**
- 拖拽邮箱到标签进行分类
- 标签排序调整
- 批量标签操作

## 🎯 简化设计原则

### 1. 功能专注
- 移除复杂的验证码获取功能
- 专注于邮箱生成、存储和管理
- 简化配置选项，只保留核心设置

### 2. 界面简洁
- 清晰的功能分区
- 直观的操作流程
- 减少不必要的界面元素

### 3. 用户友好
- 一键生成邮箱
- 可视化的标签管理
- 简单的配置界面
- 实时的状态反馈

这个简化的UI设计专注于核心的邮箱管理功能，提供了直观、高效的用户体验。
